/**
 * Version améliorée de l'outil extreme-search avec fallback Firecrawl et X Search
 * Intègre toutes les améliorations du dossier de référence
 */

import {
  generateObject,
  generateText,
  streamText,
  stepCountIs,
  tool,
} from 'ai';
import { z } from 'zod';
import { xai } from '@ai-sdk/xai';
import { myProvider } from '@/lib/ai/providers';
import {
  getContentsWithFallback,
  searchWebWithExa,
  type SearchResult,
} from './content-fetcher';
import { generateIntelligentVisualizations } from './visualization-generator';

// ============================================================================
// TYPES
// ============================================================================

export type Research = {
  text: string;
  toolResults: any[];
  sources: SearchResult[];
  charts: any[];
};

enum SearchCategory {
  NEWS = 'news',
  COMPANY = 'company',
  RESEARCH_PAPER = 'research paper',
  GITHUB = 'github',
  FINANCIAL_REPORT = 'financial report',
}

// ============================================================================
// FONCTION PRINCIPALE - EXTREME SEARCH AMÉLIORÉE
// ============================================================================

/**
 * Fonction principale qui orchestre la recherche extrême améliorée
 */
async function extremeSearchImproved(
  prompt: string,
  dataStream?: any,
): Promise<Research> {
  console.log('=== DÉBUT DE LA RECHERCHE EXTRÊME AMÉLIORÉE ===');
  console.log('Prompt:', prompt);

  const allSources: SearchResult[] = [];
  let visualizations: any[] = [];
  const charts: any[] = [];
  const toolResults: any[] = [];
  let text = '';

  try {
    // ========================================
    // ÉTAPE 1: DÉTECTION DE LA LANGUE
    // ========================================

    const languageDetection = await generateObject({
      model: myProvider.languageModel('x-fast'),
      schema: z.object({
        language: z.string().describe('ISO 639-1 language code'),
      }),
      prompt: `Detect the language of this user query and return the ISO 639-1 code. 
Focus on the language of the instruction words (like "fais", "make", "haz"), not just the subject matter.
For example: "fais une recherche sur l'AKG" is French (fr) because "fais une recherche" is French.

User query: "${prompt}"

Return the language code of the instruction/query language.`,
    });

    const userLanguage = languageDetection.object.language;
    console.log('Langue détectée:', userLanguage);

    const languageNames: { [key: string]: string } = {
      fr: 'French',
      en: 'English',
      es: 'Spanish',
      de: 'German',
      it: 'Italian',
      pt: 'Portuguese',
      ru: 'Russian',
      zh: 'Chinese',
      ja: 'Japanese',
      ko: 'Korean',
    };
    const userLanguageName = languageNames[userLanguage] || 'English';

    // ========================================
    // ÉTAPE 2: PLANIFICATION
    // ========================================

    if (dataStream) {
      dataStream.write({
        type: 'data-extreme_search',
        data: {
          kind: 'plan',
          status: { title: 'Planning research...' },
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log('📋 Génération du plan de recherche...');

    const planResult = await generateObject({
      model: myProvider.languageModel('extreme-search-model'),
      schema: z.object({
        plan: z
          .array(
            z.object({
              title: z
                .string()
                .min(10)
                .max(100)
                .describe('A concise technical title for the research section'),
              todos: z
                .array(
                  z.string().describe('Specific, technical search queries'),
                )
                .min(3)
                .max(5),
            }),
          )
          .min(1)
          .max(5),
      }),
      prompt: `Create a comprehensive technical research plan for: ${prompt}. 

Date: ${new Date().toLocaleDateString(
        userLanguage === 'fr' ? 'fr-FR' : 'en-US',
        {
          year: 'numeric',
          month: 'long',
          day: '2-digit',
          weekday: 'long',
        },
      )}

Break it down into 3-5 technical sections, each with 3-5 specific search queries.

🚨 CRITICAL LANGUAGE REQUIREMENT:
The research plan MUST be generated in ${userLanguageName} language.

Requirements:
- Create distinct technical sections covering different aspects
- Each section must have specific, technical search queries
- Focus on comprehensive coverage with diverse strategies
- Include queries for academic, technical, industry, and news sources
- Cover both current state and historical context
- Include technical implementation and practical applications`,
    });

    const plan = planResult.object.plan;
    const totalTodos = plan.reduce((acc, curr) => acc + curr.todos.length, 0);

    console.log(`📊 Plan généré avec ${totalTodos} tâches`);

    if (dataStream) {
      dataStream.write({
        type: 'data-extreme_search',
        data: {
          kind: 'plan',
          status: { title: "Plan prêt, démarrage de l'agent de recherche" },
          plan,
          timestamp: new Date().toISOString(),
        },
      });
    }

    // ========================================
    // ÉTAPE 3: EXÉCUTION AUTONOME AVEC OUTILS
    // ========================================

    console.log("🤖 Démarrage de l'agent de recherche autonome...");

    const { text: agentText } = await generateText({
      model: myProvider.languageModel('extreme-search-model'),
      stopWhen: stepCountIs(totalTodos),
      system: `You are an autonomous research agent. Your goal is to research the given plan in depth using the provided tools.

**IMPORTANT: The user's language is ${userLanguageName}. When you write summaries or notes, write them in ${userLanguageName}.**

Date: ${new Date().toLocaleDateString(
        userLanguage === 'fr' ? 'fr-FR' : 'en-US',
        {
          year: 'numeric',
          month: 'long',
          day: '2-digit',
          weekday: 'long',
        },
      )}

### PRIMARY FOCUS: RESEARCH (95% of your work)
Your main job is to RESEARCH extensively and gather comprehensive information.

⚠️ IMPORTANT: Total turn limit: maximum ${totalTodos}! You must reach this limit strictly!

For research:
- PRIORITIZE RESEARCH - Search first, search often, search exhaustively
- Launch queries one by one for better control
- Do 3-5 targeted searches per topic to get different angles
- Queries should be specific and focused, 5-15 words maximum
- Vary your approaches: overview → specific details → recent developments
- Use different categories strategically: news, research papers, company info
- Use X search for real-time discussions, public opinion, breaking news
- Verify information with multiple searches from different angles

For X search:
- The X search tool is powerful for finding recent information on X (Twitter)
- The query parameter should be specific and focused, not too broad
- Use handles to search for specific accounts
- Use startDate and endDate to limit the date range

Research plan:
${JSON.stringify(plan, null, 2)}`,
      prompt,
      temperature: 0,
      tools: {
        // ========================================
        // OUTIL: RECHERCHE WEB AMÉLIORÉE
        // ========================================
        webSearch: {
          description: 'Search the web to find information',
          inputSchema: z.object({
            query: z.string().describe('The search query').max(150),
            category: z
              .nativeEnum(SearchCategory)
              .optional()
              .describe('Catégorie de recherche'),
            includeDomains: z
              .array(z.string())
              .optional()
              .describe('Domaines à inclure'),
          }),
          execute: async (
            { query, category, includeDomains },
            { toolCallId },
          ) => {
            console.log(`🔍 Recherche web: "${query}"`);

            if (dataStream) {
              dataStream.write({
                type: 'data-extreme_search',
                data: {
                  kind: 'query',
                  queryId: toolCallId,
                  query: query,
                  status: 'started',
                  timestamp: new Date().toISOString(),
                },
              });
            }

            // Recherche avec Exa
            let results = await searchWebWithExa(query, {
              numResults: 8,
              category,
              includeDomains,
            });

            allSources.push(...results);

            // Stream les sources trouvées
            if (dataStream) {
              results.forEach((source) => {
                dataStream.write({
                  type: 'data-extreme_search',
                  data: {
                    kind: 'source',
                    queryId: toolCallId,
                    source: {
                      title: source.title,
                      url: source.url,
                      favicon: source.favicon,
                    },
                    timestamp: new Date().toISOString(),
                  },
                });
              });
            }

            // Récupère le contenu complet avec fallback Firecrawl
            if (results.length > 0) {
              try {
                if (dataStream) {
                  dataStream.write({
                    type: 'data-extreme_search',
                    data: {
                      kind: 'query',
                      queryId: toolCallId,
                      query: query,
                      status: 'reading_content',
                      timestamp: new Date().toISOString(),
                    },
                  });
                }

                const urls = results.map((r) => r.url);
                const contentsResults = await getContentsWithFallback(urls);

                if (contentsResults && contentsResults.length > 0) {
                  if (dataStream) {
                    contentsResults.forEach((content) => {
                      dataStream.write({
                        type: 'data-extreme_search',
                        data: {
                          kind: 'content',
                          queryId: toolCallId,
                          content: {
                            title: content.title || '',
                            url: content.url,
                            text: `${(content.content || '').slice(0, 500)}...`,
                            favicon: content.favicon || '',
                          },
                          timestamp: new Date().toISOString(),
                        },
                      });
                    });
                  }

                  // Merge les résultats
                  results = contentsResults.map((content) => {
                    const originalResult = results.find(
                      (r) => r.url === content.url,
                    );
                    return {
                      title: content.title || originalResult?.title || '',
                      url: content.url,
                      content: content.content || originalResult?.content || '',
                      publishedDate:
                        content.publishedDate ||
                        originalResult?.publishedDate ||
                        '',
                      favicon: content.favicon || originalResult?.favicon || '',
                    };
                  });
                }
              } catch (error) {
                console.error('❌ Erreur récupération contenu:', error);
              }
            }

            if (dataStream) {
              dataStream.write({
                type: 'data-extreme_search',
                data: {
                  kind: 'query',
                  queryId: toolCallId,
                  query: query,
                  status: 'completed',
                  timestamp: new Date().toISOString(),
                },
              });
            }

            return results.map((r) => ({
              title: r.title,
              url: r.url,
              content: r.content,
              publishedDate: r.publishedDate,
            }));
          },
        },

        // ========================================
        // OUTIL: RECHERCHE X (TWITTER)
        // ========================================
        xSearch: {
          description:
            'Recherche sur X (Twitter) pour des informations récentes et discussions',
          inputSchema: z.object({
            query: z.string().describe('Requête de recherche pour X').max(150),
            startDate: z
              .string()
              .optional()
              .describe('Date de début (YYYY-MM-DD)'),
            endDate: z.string().optional().describe('Date de fin (YYYY-MM-DD)'),
            xHandles: z
              .array(z.string())
              .optional()
              .describe('Comptes X spécifiques (sans @)'),
            maxResults: z
              .number()
              .optional()
              .describe('Nombre max de résultats (défaut 15)'),
          }),
          execute: async (
            { query, startDate, endDate, xHandles, maxResults = 15 },
            { toolCallId },
          ) => {
            console.log(`🐦 Recherche X: "${query}"`);

            const searchStartDate =
              startDate ||
              new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                .toISOString()
                .split('T')[0];
            const searchEndDate =
              endDate || new Date().toISOString().split('T')[0];

            if (dataStream) {
              dataStream.write({
                type: 'data-extreme_search',
                data: {
                  kind: 'x_search',
                  xSearchId: toolCallId,
                  query: query,
                  startDate: searchStartDate,
                  endDate: searchEndDate,
                  handles: xHandles || [],
                  status: 'started',
                  timestamp: new Date().toISOString(),
                },
              });
            }

            try {
              const { text: xText, sources } = await generateText({
                model: xai('grok-3'),
                system: `Tu es un assistant qui recherche des posts X et retourne les résultats de manière structurée. Cite les sources au format [Source No.]. Va en profondeur dans la recherche.`,
                messages: [{ role: 'user', content: query }],
                maxOutputTokens: 2000,
                providerOptions: {
                  xai: {
                    searchParameters: {
                      mode: 'on',
                      fromDate: searchStartDate,
                      toDate: searchEndDate,
                      maxSearchResults: maxResults < 15 ? 15 : maxResults,
                      returnCitations: true,
                      sources: [
                        xHandles && xHandles.length > 0
                          ? { type: 'x', xHandles: xHandles }
                          : { type: 'x' },
                      ],
                    },
                  },
                },
              });

              const result = {
                content: xText,
                citations: sources || [],
                dateRange: `${searchStartDate} à ${searchEndDate}`,
                handles: xHandles || [],
              };

              if (dataStream) {
                dataStream.write({
                  type: 'data-extreme_search',
                  data: {
                    kind: 'x_search',
                    xSearchId: toolCallId,
                    query: query,
                    startDate: searchStartDate,
                    endDate: searchEndDate,
                    handles: xHandles || [],
                    status: 'completed',
                    result: result,
                    timestamp: new Date().toISOString(),
                  },
                });
              }

              return result;
            } catch (error) {
              console.error('❌ Erreur X search:', error);

              if (dataStream) {
                dataStream.write({
                  type: 'data-extreme_search',
                  data: {
                    kind: 'x_search',
                    xSearchId: toolCallId,
                    query: query,
                    startDate: searchStartDate,
                    endDate: searchEndDate,
                    handles: xHandles || [],
                    status: 'error',
                    timestamp: new Date().toISOString(),
                  },
                });
              }

              throw error;
            }
          },
        },
      },
      onStepFinish: (step) => {
        console.log('✅ Étape terminée:', step.finishReason);
        if (step.toolResults) {
          toolResults.push(...step.toolResults);
        }
      },
    });

    text = agentText;

    // ========================================
    // ÉTAPE 4: GÉNÉRATION DES VISUALISATIONS
    // ========================================

    if (dataStream) {
      dataStream.write({
        type: 'data-extreme_search',
        data: {
          kind: 'plan',
          status: { title: 'Génération des visualisations...' },
          timestamp: new Date().toISOString(),
        },
      });
    }

    try {
      const sectionSummaries = allSources.map(
        (s) => `${s.title}: ${s.content.slice(0, 500)}`,
      );
      visualizations = await generateIntelligentVisualizations(
        prompt,
        sectionSummaries,
        allSources,
        dataStream,
      );
      charts.push(...visualizations);
      console.log(`📊 ${visualizations.length} visualisations générées`);
    } catch (error) {
      console.error('❌ Erreur génération visualisations:', error);
    }

    // ========================================
    // ÉTAPE 5: GÉNÉRATION DU RAPPORT FINAL
    // ========================================

    if (dataStream) {
      dataStream.write({
        type: 'data-extreme_search',
        data: {
          kind: 'plan',
          status: { title: 'Génération du rapport final...' },
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log('📝 Génération du rapport final structuré...');

    // Préparer le contexte pour le rapport
    const sourcesContext = allSources
      .map(
        (s, i) =>
          `[${i + 1}] ${s.title}\n${s.content.slice(0, 800)}...\nURL: ${s.url}`,
      )
      .join('\n\n---\n\n');

    // Générer un rapport structuré en Markdown avec streaming progressif
    const finalReportStream = streamText({
      model: myProvider.languageModel('extreme-search-model'),
      system: `You are an expert research analyst. Your task is to synthesize research findings into a comprehensive, well-structured report in Markdown format.

CRITICAL REQUIREMENTS:
- **IMPORTANT: Write ENTIRELY in ${userLanguageName} language. ALL text, headers, and content MUST be in ${userLanguageName}.**
- The user's query language is ${userLanguageName}, so your entire report MUST be in ${userLanguageName}
- Use proper Markdown formatting (headers, lists, tables, bold, italic)
- Structure the report with clear sections and subsections
- Include specific data, statistics, and citations
- Use tables for comparative data
- Be comprehensive and detailed (aim for 2000+ words)
- Include a table of key metrics/facts
- Cite sources with [Source X] notation
- Professional, academic tone

REPORT STRUCTURE:
1. # Title (based on the research topic)
2. ## Executive Summary (2-3 paragraphs)
3. ## Detailed Sections (based on research findings)
   - Use ### for subsections
   - Include bullet points for key facts
   - Add tables for data comparison
4. ## Key Metrics Table
5. ## Conclusions and Recommendations
6. ## Sources

DO NOT:
- Write meta-commentary about the research process
- Mention "I searched" or "I found"
- Include placeholder text
- Be vague or general

WRITE AS IF:
- You are presenting to an expert audience
- Every statement is backed by the research
- The report will be published in a scientific journal`,
      prompt: `Based on the following research findings, create a comprehensive, detailed report about: "${prompt}"

**CRITICAL: Write your ENTIRE response in ${userLanguageName} language. Do NOT write in any other language.**

RESEARCH FINDINGS:
${text}

SOURCES (${allSources.length} total):
${sourcesContext.slice(0, 15000)}

Create a professional, detailed report in Markdown format in ${userLanguageName}. Be specific, include data, and structure it clearly.`,
      maxTokens: 4000,
    });

    // Streamer le rapport progressivement
    let accumulatedText = '';
    const reportId = 'final-report';

    console.log('📝 Streaming du rapport final...');

    for await (const delta of finalReportStream.textStream) {
      accumulatedText += delta;

      // Envoyer chaque chunk au client pour affichage en temps réel
      if (dataStream) {
        dataStream.write({
          type: 'data-extreme_search',
          data: {
            kind: 'report',
            reportId: reportId,
            content: accumulatedText,
            status: 'writing',
            timestamp: new Date().toISOString(),
          },
        });
      }
    }

    // Marquer le rapport comme terminé
    if (dataStream) {
      dataStream.write({
        type: 'data-extreme_search',
        data: {
          kind: 'report',
          reportId: reportId,
          content: accumulatedText,
          status: 'completed',
          timestamp: new Date().toISOString(),
        },
      });
    }

    text = accumulatedText;
    console.log(`📝 Rapport final streamé (${text.length} caractères)`);

    // ========================================
    // ÉTAPE 6: FINALISATION
    // ========================================

    if (dataStream) {
      dataStream.write({
        type: 'data-extreme_search',
        data: {
          kind: 'plan',
          status: { title: 'Recherche terminée' },
          timestamp: new Date().toISOString(),
        },
      });
    }

    console.log('✅ Recherche extrême terminée');
    console.log(`📊 ${allSources.length} sources collectées`);
    console.log(`🔧 ${toolResults.length} résultats d'outils`);
    console.log(`📈 ${charts.length} visualisations`);
    console.log(`📝 Rapport final généré (${text.length} caractères)`);

    // Déduplique les sources par URL
    const uniqueSources = Array.from(
      new Map(
        allSources.map((s) => [
          s.url,
          { ...s, content: `${s.content.slice(0, 3000)}...` },
        ]),
      ).values(),
    );

    return {
      text,
      toolResults,
      sources: uniqueSources,
      charts,
    };
  } catch (error) {
    console.error('❌ Erreur dans extremeSearchImproved:', error);
    throw error;
  }
}

// ============================================================================
// EXPORT DE L'OUTIL
// ============================================================================

/**
 * Crée l'outil de recherche extrême amélioré pour l'utiliser avec Vercel AI SDK
 */
export function extremeSearchToolImproved(dataStream?: any) {
  return tool({
    description:
      'Effectue une recherche extrême approfondie améliorée avec fallback Firecrawl et recherche X (Twitter).',
    inputSchema: z.object({
      prompt: z
        .string()
        .describe(
          "Le prompt exact de l'utilisateur. Ne pas modifier ou inférer.",
        ),
    }),
    execute: async ({ prompt }) => {
      console.log(
        '🚀 Démarrage de la recherche extrême améliorée pour:',
        prompt,
      );

      const research = await extremeSearchImproved(prompt, dataStream);

      return {
        research: {
          text: research.text,
          toolResults: research.toolResults,
          sources: research.sources,
          charts: research.charts,
        },
      };
    },
  });
}

// Export par défaut
export default extremeSearchToolImproved;
