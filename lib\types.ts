import { z } from 'zod';
import type { getWeather } from './ai/tools/get-weather';
import type { createDocument } from './ai/tools/create-document';
import type { updateDocument } from './ai/tools/update-document';
import type { requestSuggestions } from './ai/tools/request-suggestions';
import type { InferUITool, UIMessage } from 'ai';

import type { ArtifactKind } from '@/components/artifact';
import type { Suggestion } from './db/schema';

export type DataPart = { type: 'append-message'; message: string };

export interface SearchResult {
  title: string;
  link: string;
  favicon: string;
}

export interface PlaceImage {
  url: string;
  description?: string;
  thumbnail?: string;
  width?: number;
  height?: number;
}

export interface Place {
  cid: string;
  title: string;
  address: string;
  latitude: number;
  longitude: number;
  rating: number;
  category: string;
  phoneNumber?: string;
  website?: string;
  images?: PlaceImage[];
}

export const messageMetadataSchema = z.object({
  createdAt: z.string(),
});

export type MessageMetadata = z.infer<typeof messageMetadataSchema>;

type weatherTool = InferUITool<typeof getWeather>;
type createDocumentTool = InferUITool<ReturnType<typeof createDocument>>;
type updateDocumentTool = InferUITool<ReturnType<typeof updateDocument>>;
type requestSuggestionsTool = InferUITool<
  ReturnType<typeof requestSuggestions>
>;

export type ChatTools = {
  getWeather: weatherTool;
  createDocument: createDocumentTool;
  updateDocument: updateDocumentTool;
  requestSuggestions: requestSuggestionsTool;
};

export type CustomUIDataTypes = {
  textDelta: string;
  imageDelta: string;
  sheetDelta: string;
  codeDelta: string;
  htmlDelta: string;
  suggestion: Suggestion;
  appendMessage: string;
  id: string;
  title: string;
  kind: ArtifactKind;
  status: string;
  clear: null;
  finish: null;
  map: { query: string; places: any };
  youtube: { query: string; videos: any[]; resultsCount: number };
  extreme_search: {
    kind: 'plan' | 'section' | 'query' | 'source' | 'content';
    status: string | { title: string };
    timestamp: string;
    queryId?: string;
    plan?: any[];
    section?: string;
    query?: string;
    source?: {
      url: string;
      title: string;
      favicon: string;
    };
    content?: {
      title: string;
      text: string;
      url: string;
    };
    completed?: boolean;
    hasErrors?: boolean;
  };
};

export type ChatMessage = UIMessage<
  MessageMetadata,
  CustomUIDataTypes,
  ChatTools
>;

export interface Attachment {
  name: string;
  url: string;
  contentType: string;
}

// Type pour les data parts de extreme search (AI SDK 5)
export type DataExtremeSearchPart = {
  type: 'data-extreme_search';
  data:
    | {
        // Plan initial et mises à jour de statut généralisées
        kind: 'plan';
        status: { title: string };
        plan?: Array<{ title: string; todos: string[] }>;
      }
    | {
        // État d'une requête de recherche
        kind: 'query';
        queryId: string;
        query: string;
        status: 'started' | 'reading_content' | 'completed' | 'error';
      }
    | {
        // Source trouvée pour une requête
        kind: 'source';
        queryId: string;
        source: { title: string; url: string; favicon?: string };
      }
    | {
        // Contenu extrait pour une source
        kind: 'content';
        queryId: string;
        content: { title: string; url: string; text: string; favicon?: string };
      }
    | {
        // Indication d'une section en cours (optionnel selon implémentation)
        kind: 'section';
        section: string;
        status: string;
      }
    | {
        // Rapport final en cours de génération (streaming progressif)
        kind: 'report';
        reportId: string;
        content: string;
        status: 'writing' | 'completed' | 'error';
        timestamp: string;
      };
};

// Type pour les data parts de query completion (multi-search)
export type DataQueryCompletionPart = {
  type: 'query_completion';
  data: {
    query: string;
    index: number;
    total: number;
    status: 'started' | 'completed' | 'error';
    resultsCount: number;
    imagesCount: number;
  };
};
